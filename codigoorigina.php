<?php

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['date']) && !empty($_POST['message'])  ) {

  $apiKey = '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';

  $email=$_POST['correo'];
  

  $data = [
    "sender" => [
      "name" => "Digital Solution Department",
      "email" => "<EMAIL>"
    ],
    "to" => [
      [
        "email" => '<EMAIL>',
        "name" => 'Digital Solution Department'
      ]
    ],
    "subject" => "Digital Solution Department request",
    "htmlContent" => '<html><body>
        
        <div style="font-family: Arial, sans-serif; padding: 30px; background-color: #ffffff; max-width: 600px; margin: auto; border: 1px solid #e0e0e0;">
  <div style="text-align: center; margin-bottom: 30px;">
    <img src="https://digitalsolutionsdepartment.com/img_DSD/logo-DSD.png" alt="Digital Solution Department" style="max-height: 180px;">
  </div>

  <h2 style="text-align: center; font-weight: normal;"> Hi Digital Solution Department</h2>
  <p style="text-align: center; font-size: 16px; color: #555;">Here New Appointment info request</p>

  <p style="font-size: 16px; margin-top: 40px;"><strong>Details:</strong></p>

  <table style="width: 100%; border-collapse: collapse; font-size: 15px;">
    <tr style="background-color: #f5f5f5;">
      <td style="padding: 10px; font-weight: bold;">Name</td>
      <td style="padding: 10px;">' . htmlspecialchars($_POST['name']) . '</td>
    </tr>
    <tr style="background-color: #f5f5f5;">
      <td style="padding: 10px; font-weight: bold;">Email</td>
      <td style="padding: 10px; text-decoration:none; color:black;">' . htmlspecialchars($_POST['email']) . '</td>
    </tr>
    <tr style="background-color: #f5f5f5;">
      <td style="padding: 10px; font-weight: bold; vertical-align: top;">Date</td>
      <td style="padding: 10px;">' . nl2br(htmlspecialchars($_POST['date'])) . '</td>
    </tr>
    <tr style="background-color: #f5f5f5;">
      <td style="padding: 10px; font-weight: bold; vertical-align: top;">Message</td>
      <td style="padding: 10px;">' . nl2br(htmlspecialchars($_POST['message'])) . '</td>
    </tr>
  </table>
</div>        
        </body></html>'
  ];

  $ch = curl_init();

  curl_setopt($ch, CURLOPT_URL, "https://api.brevo.com/v3/smtp/email");
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_POST, true);
  curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "accept: application/json",
    "api-key: $apiKey",
    "content-type: application/json"
  ]);
  curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

  $response = curl_exec($ch);

  if (curl_errno($ch)) {
    echo "Error al enviar: " . curl_error($ch);
  } else {
    echo "Correo enviado correctamente: $response";
  }

  curl_close($ch);

} 


?>